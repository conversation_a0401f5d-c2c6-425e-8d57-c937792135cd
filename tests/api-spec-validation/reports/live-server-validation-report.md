# API Spec Validation Report

Generated: 2025-08-05T04:39:55+05:30

## Summary

- Total Endpoints: 172
- Passed: 45
- Failed: 127
- Warnings: 0
- Success Rate: 26.16%

## Detailed Results

### ✅ GET /orchestrator/app/history/deployed-configuration/all/{appId}/{pipelineId}/{wfrId}

- **Status**: PASS
- **Duration**: 565.163625ms
- **Spec File**: ../../specs/deployment/rollback.yaml
- **Response Code**: 200

---

### ❌ GET /orchestrator/app/deployment-configuration/all/latest/{appId}/{pipelineId}

- **Status**: FAIL
- **Duration**: 110.777375ms
- **Spec File**: ../../specs/deployment/rollback.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ GET /orchestrator/app/history/deployed-configuration/all/latest/{appId}/{pipelineId}

- **Status**: FAIL
- **Duration**: 111.919333ms
- **Spec File**: ../../specs/deployment/rollback.yaml
- **Response Code**: 400

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 400

---

### ✅ GET /orchestrator/external-links/tools

- **Status**: PASS
- **Duration**: 114.352209ms
- **Spec File**: ../../specs/external-links/external-links-specs.yaml
- **Response Code**: 200

---

### ❌ DELETE /orchestrator/external-links

- **Status**: FAIL
- **Duration**: 111.734125ms
- **Spec File**: ../../specs/external-links/external-links-specs.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ✅ GET /orchestrator/external-links

- **Status**: PASS
- **Duration**: 116.120625ms
- **Spec File**: ../../specs/external-links/external-links-specs.yaml
- **Response Code**: 200

---

### ❌ POST /orchestrator/external-links

- **Status**: FAIL
- **Duration**: 114.137959ms
- **Spec File**: ../../specs/external-links/external-links-specs.yaml
- **Response Code**: 400

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 400

---

### ❌ PUT /orchestrator/external-links

- **Status**: FAIL
- **Duration**: 114.748625ms
- **Spec File**: ../../specs/external-links/external-links-specs.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ✅ GET /orchestrator/notification/channel

- **Status**: PASS
- **Duration**: 113.732375ms
- **Spec File**: ../../specs/notifications/core.yaml
- **Response Code**: 200

---

### ❌ POST /orchestrator/notification/channel

- **Status**: FAIL
- **Duration**: 113.271ms
- **Spec File**: ../../specs/notifications/core.yaml
- **Response Code**: 200

**Issues:**
- **RESPONSE_FORMAT_ERROR**: Response is not valid JSON: unexpected end of JSON input

---

### ❌ GET /orchestrator/notification/recipient

- **Status**: FAIL
- **Duration**: 110.513333ms
- **Spec File**: ../../specs/notifications/core.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ✅ DELETE /orchestrator/notification

- **Status**: PASS
- **Duration**: 115.465958ms
- **Spec File**: ../../specs/notifications/core.yaml
- **Response Code**: 200

---

### ❌ GET /orchestrator/notification

- **Status**: FAIL
- **Duration**: 110.511792ms
- **Spec File**: ../../specs/notifications/core.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ POST /orchestrator/notification

- **Status**: FAIL
- **Duration**: 138.00775ms
- **Spec File**: ../../specs/notifications/core.yaml
- **Response Code**: 400

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 400

---

### ❌ PUT /orchestrator/notification

- **Status**: FAIL
- **Duration**: 113.781792ms
- **Spec File**: ../../specs/notifications/core.yaml
- **Response Code**: 400

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 400

---

### ❌ PUT /orchestrator/application/rollback

- **Status**: FAIL
- **Duration**: 115.071ms
- **Spec File**: ../../specs/openapiClient/api/openapi.yaml
- **Response Code**: 500

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 500

---

### ❌ POST /orchestrator/application/template-chart

- **Status**: FAIL
- **Duration**: 132.861417ms
- **Spec File**: ../../specs/openapiClient/api/openapi.yaml
- **Response Code**: 500

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 500

---

### ✅ GET /orchestrator/app/history/deployed-configuration/{appId}/{pipelineId}/{wfrId}

- **Status**: PASS
- **Duration**: 117.460625ms
- **Spec File**: ../../specs/audit/api-changes.yaml
- **Response Code**: 200

---

### ❌ GET /orchestrator/app/history/deployed-component/detail/{appId}/{pipelineId}/{id}

- **Status**: FAIL
- **Duration**: 115.314833ms
- **Spec File**: ../../specs/audit/api-changes.yaml
- **Response Code**: 400

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 400

---

### ❌ GET /orchestrator/app/history/deployed-component/list/{appId}/{pipelineId}

- **Status**: FAIL
- **Duration**: 113.236792ms
- **Spec File**: ../../specs/audit/api-changes.yaml
- **Response Code**: 400

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 400

---

### ❌ GET /orchestrator/app/ci-pipeline/{ciPipelineId}/linked-ci/downstream/env

- **Status**: FAIL
- **Duration**: 114.827584ms
- **Spec File**: ../../specs/ci-pipeline/ciPipelineDownstream/downstream-linked-ci-view-spec.yaml
- **Response Code**: 400

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 400

---

### ❌ GET /orchestrator/app/ci-pipeline/{ciPipelineId}/linked-ci/downstream/cd

- **Status**: FAIL
- **Duration**: 113.969834ms
- **Spec File**: ../../specs/ci-pipeline/ciPipelineDownstream/downstream-linked-ci-view-spec.yaml
- **Response Code**: 400

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 400

---

### ❌ GET /orchestrator/flux-application

- **Status**: FAIL
- **Duration**: 114.508625ms
- **Spec File**: ../../specs/gitops/fluxcd.yaml
- **Response Code**: 400

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 400

---

### ❌ GET /orchestrator/flux-application/app

- **Status**: FAIL
- **Duration**: 113.6435ms
- **Spec File**: ../../specs/gitops/fluxcd.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ POST /config

- **Status**: FAIL
- **Duration**: 110.716041ms
- **Spec File**: ../../specs/gitops/validation.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ PUT /config

- **Status**: FAIL
- **Duration**: 124.027291ms
- **Spec File**: ../../specs/gitops/validation.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ GET /gitops/config

- **Status**: FAIL
- **Duration**: 111.535417ms
- **Spec File**: ../../specs/gitops/validation.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ GET /gitops/config-by-provider

- **Status**: FAIL
- **Duration**: 111.972333ms
- **Spec File**: ../../specs/gitops/validation.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404
- **RESPONSE_FORMAT_ERROR**: Response is not valid JSON: invalid character '<' looking for beginning of value

---

### ❌ GET /gitops/config/{id}

- **Status**: FAIL
- **Duration**: 111.631417ms
- **Spec File**: ../../specs/gitops/validation.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404
- **RESPONSE_FORMAT_ERROR**: Response is not valid JSON: invalid character '<' looking for beginning of value

---

### ❌ GET /gitops/configured

- **Status**: FAIL
- **Duration**: 109.9925ms
- **Spec File**: ../../specs/gitops/validation.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ POST /orchestrator/validate

- **Status**: FAIL
- **Duration**: 109.837667ms
- **Spec File**: ../../specs/gitops/validation.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ✅ GET /orchestrator/app-store/installed-app

- **Status**: PASS
- **Duration**: 118.630167ms
- **Spec File**: ../../specs/helm/charts.yaml
- **Response Code**: 200

---

### ❌ GET /orchestrator/app-store/installed-app/notes/{installed-app-id}/{env-id}

- **Status**: FAIL
- **Duration**: 111.528ms
- **Spec File**: ../../specs/helm/charts.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ✅ GET /orchestrator/deployment/template/fetch

- **Status**: PASS
- **Duration**: 124.301709ms
- **Spec File**: ../../specs/deployment/core.yaml
- **Response Code**: 200

---

### ✅ PUT /orchestrator/deployment/template/upload

- **Status**: PASS
- **Duration**: 113.838167ms
- **Spec File**: ../../specs/deployment/core.yaml
- **Response Code**: 200

---

### ❌ POST /orchestrator/deployment/template/validate

- **Status**: FAIL
- **Duration**: 112.558291ms
- **Spec File**: ../../specs/deployment/core.yaml
- **Response Code**: 400

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 400

---

### ❌ GET /orchestrator/app/deployment-status/timeline/{appId}/{envId}

- **Status**: FAIL
- **Duration**: 116.338375ms
- **Spec File**: ../../specs/deployment/timeline.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ GET /orchestrator/env/namespace/autocomplete

- **Status**: FAIL
- **Duration**: 112.584791ms
- **Spec File**: ../../specs/environment/core.yaml
- **Response Code**: 500

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 500

---

### ❌ POST /orchestrator/gitops/validate

- **Status**: FAIL
- **Duration**: 112.98575ms
- **Spec File**: ../../specs/gitops/core.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ✅ GET /orchestrator/gitops/config

- **Status**: PASS
- **Duration**: 112.4935ms
- **Spec File**: ../../specs/gitops/core.yaml
- **Response Code**: 200

---

### ❌ POST /orchestrator/gitops/config

- **Status**: FAIL
- **Duration**: 114.059083ms
- **Spec File**: ../../specs/gitops/core.yaml
- **Response Code**: 400

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 400

---

### ❌ PUT /orchestrator/gitops/config

- **Status**: FAIL
- **Duration**: 114.762834ms
- **Spec File**: ../../specs/gitops/core.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ GET /orchestrator/gitops/config-by-provider

- **Status**: FAIL
- **Duration**: 113.325583ms
- **Spec File**: ../../specs/gitops/core.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ✅ GET /orchestrator/gitops/config/{id}

- **Status**: PASS
- **Duration**: 115.674417ms
- **Spec File**: ../../specs/gitops/core.yaml
- **Response Code**: 200

---

### ✅ GET /orchestrator/gitops/configured

- **Status**: PASS
- **Duration**: 114.820333ms
- **Spec File**: ../../specs/gitops/core.yaml
- **Response Code**: 200

---

### ✅ GET /orchestrator/chartref/autocomplete/{appId}

- **Status**: PASS
- **Duration**: 115.320042ms
- **Spec File**: ../../specs/helm/dynamic-charts.yaml
- **Response Code**: 200

---

### ❌ POST /orchestrator/job

- **Status**: FAIL
- **Duration**: 115.067083ms
- **Spec File**: ../../specs/jobs/core.yaml
- **Response Code**: 400

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 400

---

### ❌ GET /job/ci-pipeline/list/{jobId}

- **Status**: FAIL
- **Duration**: 113.159833ms
- **Spec File**: ../../specs/jobs/core.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ POST /job/list

- **Status**: FAIL
- **Duration**: 109.870542ms
- **Spec File**: ../../specs/jobs/core.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ POST /orchestrator/cluster/access

- **Status**: FAIL
- **Duration**: 113.057917ms
- **Spec File**: ../../specs/kubernetes/access-policy.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ PUT /orchestrator/cluster/access

- **Status**: FAIL
- **Duration**: 110.685958ms
- **Spec File**: ../../specs/kubernetes/access-policy.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ GET /orchestrator/cluster/access/list

- **Status**: FAIL
- **Duration**: 112.798417ms
- **Spec File**: ../../specs/kubernetes/access-policy.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ DELETE /orchestrator/cluster/access/{id}

- **Status**: FAIL
- **Duration**: 110.181417ms
- **Spec File**: ../../specs/kubernetes/access-policy.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404
- **RESPONSE_FORMAT_ERROR**: Response is not valid JSON: invalid character 'p' after top-level value

---

### ❌ GET /orchestrator/cluster/access/{id}

- **Status**: FAIL
- **Duration**: 110.525125ms
- **Spec File**: ../../specs/kubernetes/access-policy.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404
- **RESPONSE_FORMAT_ERROR**: Response is not valid JSON: invalid character 'p' after top-level value

---

### ❌ POST /orchestrator/webhook/git/{gitHostId}

- **Status**: FAIL
- **Duration**: 113.14125ms
- **Spec File**: ../../specs/notifications/webhooks.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ POST /orchestrator/webhook/git/{gitHostId}/{secret}

- **Status**: FAIL
- **Duration**: 112.597208ms
- **Spec File**: ../../specs/notifications/webhooks.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ POST /orchestrator/webhook/notification

- **Status**: FAIL
- **Duration**: 110.103667ms
- **Spec File**: ../../specs/notifications/webhooks.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ GET /orchestrator/webhook/notification/variables

- **Status**: FAIL
- **Duration**: 112.984458ms
- **Spec File**: ../../specs/notifications/webhooks.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ GET /orchestrator/webhook/notification/{id}

- **Status**: FAIL
- **Duration**: 287.334917ms
- **Spec File**: ../../specs/notifications/webhooks.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ POST /orchestrator/webhook/ci/workflow

- **Status**: FAIL
- **Duration**: 115.331584ms
- **Spec File**: ../../specs/notifications/webhooks.yaml
- **Response Code**: 500

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 500

---

### ❌ POST /orchestrator/webhook/ext-ci/{externalCiId}

- **Status**: FAIL
- **Duration**: 113.234583ms
- **Spec File**: ../../specs/notifications/webhooks.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ POST /orchestrator/webhook/git

- **Status**: FAIL
- **Duration**: 114.403458ms
- **Spec File**: ../../specs/notifications/webhooks.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ✅ GET /orchestrator/app-store/discover/application/{appStoreId}/version/autocomplete

- **Status**: PASS
- **Duration**: 118.960625ms
- **Spec File**: ../../specs/app-store.yaml
- **Response Code**: 200

---

### ✅ GET /orchestrator/app-store/discover/application/{id}

- **Status**: PASS
- **Duration**: 326.005708ms
- **Spec File**: ../../specs/app-store.yaml
- **Response Code**: 200

---

### ❌ GET /orchestrator/app-store/discover/search

- **Status**: FAIL
- **Duration**: 117.765834ms
- **Spec File**: ../../specs/app-store.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ✅ GET /orchestrator/app-store/discover

- **Status**: PASS
- **Duration**: 410.844291ms
- **Spec File**: ../../specs/app-store.yaml
- **Response Code**: 200

---

### ✅ GET /orchestrator/app-store/discover/application/chartInfo/{appStoreApplicationVersionId}

- **Status**: PASS
- **Duration**: 187.426333ms
- **Spec File**: ../../specs/app-store.yaml
- **Response Code**: 200

---

### ❌ PATCH /orchestrator/app/ci-pipeline/patch-source

- **Status**: FAIL
- **Duration**: 112.779125ms
- **Spec File**: ../../specs/ci-pipeline/ci-pipeline-change-source.yaml
- **Response Code**: 500

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 500

---

### ✅ GET /orchestrator/version

- **Status**: PASS
- **Duration**: 112.723208ms
- **Spec File**: ../../specs/common/version.yaml
- **Response Code**: 200

---

### ❌ GET /orchestrator/app/template/default/{appId}/{chartRefId}

- **Status**: FAIL
- **Duration**: 117.840417ms
- **Spec File**: ../../specs/environment/templates.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ✅ GET /orchestrator/chart-repo/{id}

- **Status**: PASS
- **Duration**: 116.531709ms
- **Spec File**: ../../specs/helm/provider.yaml
- **Response Code**: 200

---

### ✅ GET /orchestrator/chart-group/list

- **Status**: PASS
- **Duration**: 113.05725ms
- **Spec File**: ../../specs/helm/provider.yaml
- **Response Code**: 200

---

### ❌ POST /orchestrator/chart-repo

- **Status**: FAIL
- **Duration**: 108.769583ms
- **Spec File**: ../../specs/helm/provider.yaml
- **Response Code**: 405

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 405

---

### ❌ PUT /orchestrator/chart-repo

- **Status**: FAIL
- **Duration**: 111.368666ms
- **Spec File**: ../../specs/helm/provider.yaml
- **Response Code**: 405

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 405

---

### ✅ GET /orchestrator/chart-repo/list

- **Status**: PASS
- **Duration**: 117.285792ms
- **Spec File**: ../../specs/helm/provider.yaml
- **Response Code**: 200

---

### ❌ POST /orchestrator/chart-repo/sync

- **Status**: FAIL
- **Duration**: 111.242834ms
- **Spec File**: ../../specs/helm/provider.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ POST /orchestrator/app-store/chart-provider/update

- **Status**: FAIL
- **Duration**: 114.223458ms
- **Spec File**: ../../specs/helm/provider.yaml
- **Response Code**: 400

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 400

---

### ❌ POST /orchestrator/chart-group/entries

- **Status**: FAIL
- **Duration**: 112.158709ms
- **Spec File**: ../../specs/helm/provider.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ✅ GET /orchestrator/app-store/chart-provider/list

- **Status**: PASS
- **Duration**: 118.56425ms
- **Spec File**: ../../specs/helm/provider.yaml
- **Response Code**: 200

---

### ❌ PUT /orchestrator/chart-group

- **Status**: FAIL
- **Duration**: 225.73175ms
- **Spec File**: ../../specs/helm/provider.yaml
- **Response Code**: 405

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 405

---

### ❌ POST /orchestrator/chart-group

- **Status**: FAIL
- **Duration**: 225.143041ms
- **Spec File**: ../../specs/helm/provider.yaml
- **Response Code**: 405

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 405

---

### ❌ DELETE /orchestrator/chart-group/{id}

- **Status**: FAIL
- **Duration**: 111.297292ms
- **Spec File**: ../../specs/helm/provider.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ POST /orchestrator/chart-repo/validate

- **Status**: FAIL
- **Duration**: 112.372833ms
- **Spec File**: ../../specs/helm/provider.yaml
- **Response Code**: 400

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 400

---

### ❌ POST /orchestrator/app-store/chart-provider/sync-chart

- **Status**: FAIL
- **Duration**: 114.302125ms
- **Spec File**: ../../specs/helm/provider.yaml
- **Response Code**: 400

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 400

---

### ❌ POST /orchestrator/validate

- **Status**: FAIL
- **Duration**: 108.705417ms
- **Spec File**: ../../specs/helm/repo-validation.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ POST /update

- **Status**: FAIL
- **Duration**: 110.086167ms
- **Spec File**: ../../specs/helm/repo-validation.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ POST /create

- **Status**: FAIL
- **Duration**: 109.668792ms
- **Spec File**: ../../specs/helm/repo-validation.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ✅ GET /orchestrator/cluster

- **Status**: PASS
- **Duration**: 119.445583ms
- **Spec File**: ../../specs/kubernetes/cluster.yaml
- **Response Code**: 200

---

### ❌ PUT /orchestrator/cluster

- **Status**: FAIL
- **Duration**: 115.156541ms
- **Spec File**: ../../specs/kubernetes/cluster.yaml
- **Response Code**: 400

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 400

---

### ✅ GET /orchestrator/cluster/auth-list

- **Status**: PASS
- **Duration**: 112.9055ms
- **Spec File**: ../../specs/kubernetes/cluster.yaml
- **Response Code**: 200

---

### ❌ DELETE /orchestrator/k8s/resources/ephemeralContainers

- **Status**: FAIL
- **Duration**: 112.340708ms
- **Spec File**: ../../specs/kubernetes/ephemeral-containers.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ POST /orchestrator/k8s/resources/ephemeralContainers

- **Status**: FAIL
- **Duration**: 112.369292ms
- **Spec File**: ../../specs/kubernetes/ephemeral-containers.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ POST /ci-pipeline/trigger

- **Status**: FAIL
- **Duration**: 109.048667ms
- **Spec File**: ../../specs/ci-pipeline/ci-pipeline-build-spec.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ GET /orchestrator/commit-info/{ciPipelineMaterialId}/{gitHash}

- **Status**: FAIL
- **Duration**: 111.545666ms
- **Spec File**: ../../specs/ci-pipeline/ci-pipeline-build-spec.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404
- **RESPONSE_FORMAT_ERROR**: Response is not valid JSON: invalid character 'p' after top-level value

---

### ❌ GET /{appId}/ci-pipeline/{pipelineId}/workflow/{workflowId}

- **Status**: FAIL
- **Duration**: 112.489333ms
- **Spec File**: ../../specs/ci-pipeline/ci-pipeline-build-spec.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404
- **RESPONSE_FORMAT_ERROR**: Response is not valid JSON: invalid character '<' looking for beginning of value

---

### ❌ POST /orchestrator/app/cd-pipeline/patch/deployment

- **Status**: FAIL
- **Duration**: 113.475625ms
- **Spec File**: ../../specs/deployment/app-type-change.yaml
- **Response Code**: 400

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 400

---

### ✅ GET /orchestrator/git/host

- **Status**: PASS
- **Duration**: 114.475958ms
- **Spec File**: ../../specs/gitops/submodules.yaml
- **Response Code**: 200

---

### ❌ POST /orchestrator/git/host

- **Status**: FAIL
- **Duration**: 113.475583ms
- **Spec File**: ../../specs/gitops/submodules.yaml
- **Response Code**: 400

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 400

---

### ❌ GET /orchestrator/git/host/event/{eventId}

- **Status**: FAIL
- **Duration**: 117.662291ms
- **Spec File**: ../../specs/gitops/submodules.yaml
- **Response Code**: 500

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 500

---

### ❌ GET /orchestrator/git/host/webhook-meta-config/{gitProviderId}

- **Status**: FAIL
- **Duration**: 116.912958ms
- **Spec File**: ../../specs/gitops/submodules.yaml
- **Response Code**: 500

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 500

---

### ❌ GET /orchestrator/git/host/{id}

- **Status**: FAIL
- **Duration**: 116.758292ms
- **Spec File**: ../../specs/gitops/submodules.yaml
- **Response Code**: 500

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 500

---

### ❌ GET /orchestrator/git/host/{id}/event

- **Status**: FAIL
- **Duration**: 118.603292ms
- **Spec File**: ../../specs/gitops/submodules.yaml
- **Response Code**: 500

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 500

---

### ❌ PATCH /orchestrator/app/env/patch

- **Status**: FAIL
- **Duration**: 126.061083ms
- **Spec File**: ../../specs/helm/deployment-chart-type.yaml
- **Response Code**: 400

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 400

---

### ❌ POST /orchestrator/app/ci-pipeline/patch

- **Status**: FAIL
- **Duration**: 120.701375ms
- **Spec File**: ../../specs/infrastructure/docker-build.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ✅ GET /orchestrator/app/ci-pipeline/{appId}

- **Status**: PASS
- **Duration**: 127.46025ms
- **Spec File**: ../../specs/infrastructure/docker-build.yaml
- **Response Code**: 200

---

### ✅ GET /orchestrator/app/wf/all/component-names/{appId}

- **Status**: PASS
- **Duration**: 118.140667ms
- **Spec File**: ../../specs/infrastructure/docker-build.yaml
- **Response Code**: 200

---

### ✅ GET /orchestrator/module

- **Status**: PASS
- **Duration**: 120.957583ms
- **Spec File**: ../../specs/modularisation/v1.yaml
- **Response Code**: 200

---

### ❌ POST /orchestrator/module

- **Status**: FAIL
- **Duration**: 111.423333ms
- **Spec File**: ../../specs/modularisation/v1.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ POST /orchestrator/server

- **Status**: FAIL
- **Duration**: 113.392334ms
- **Spec File**: ../../specs/modularisation/v1.yaml
- **Response Code**: 400

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 400

---

### ✅ GET /orchestrator/server

- **Status**: PASS
- **Duration**: 113.427458ms
- **Spec File**: ../../specs/modularisation/v1.yaml
- **Response Code**: 200

---

### ❌ POST /orchestrator/user

- **Status**: FAIL
- **Duration**: 113.313083ms
- **Spec File**: ../../specs/security/user-management.yaml
- **Response Code**: 400

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 400

---

### ❌ PUT /orchestrator/user

- **Status**: FAIL
- **Duration**: 115.393167ms
- **Spec File**: ../../specs/security/user-management.yaml
- **Response Code**: 400

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 400

---

### ❌ DELETE /orchestrator/user/bulk

- **Status**: FAIL
- **Duration**: 116.402625ms
- **Spec File**: ../../specs/security/user-management.yaml
- **Response Code**: 500

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 500

---

### ✅ GET /orchestrator/user/v2

- **Status**: PASS
- **Duration**: 116.770583ms
- **Spec File**: ../../specs/security/user-management.yaml
- **Response Code**: 200

---

### ❌ DELETE /orchestrator/user/{id}

- **Status**: FAIL
- **Duration**: 116.3865ms
- **Spec File**: ../../specs/security/user-management.yaml
- **Response Code**: 400

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 400

---

### ✅ GET /orchestrator/user/{id}

- **Status**: PASS
- **Duration**: 115.555625ms
- **Spec File**: ../../specs/security/user-management.yaml
- **Response Code**: 200

---

### ✅ GET /orchestrator/plugin/global/detail/all

- **Status**: PASS
- **Duration**: 951.2835ms
- **Spec File**: ../../specs/plugins/global.yaml
- **Response Code**: 200

---

### ❌ POST /orchestrator/plugin/global/list/detail/v2

- **Status**: FAIL
- **Duration**: 114.427875ms
- **Spec File**: ../../specs/plugins/global.yaml
- **Response Code**: 400

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 400

---

### ✅ GET /orchestrator/plugin/global/list/v2

- **Status**: PASS
- **Duration**: 192.531667ms
- **Spec File**: ../../specs/plugins/global.yaml
- **Response Code**: 200

---

### ✅ GET /orchestrator/plugin/global/detail/{pluginId}

- **Status**: PASS
- **Duration**: 119.820084ms
- **Spec File**: ../../specs/plugins/global.yaml
- **Response Code**: 200

---

### ✅ GET /orchestrator/plugin/global/list/tags

- **Status**: PASS
- **Duration**: 379.7555ms
- **Spec File**: ../../specs/plugins/global.yaml
- **Response Code**: 200

---

### ✅ GET /orchestrator/plugin/global/list/v2/min

- **Status**: PASS
- **Duration**: 114.401375ms
- **Spec File**: ../../specs/plugins/global.yaml
- **Response Code**: 200

---

### ❌ POST /orchestrator/plugin/global/create

- **Status**: FAIL
- **Duration**: 113.928459ms
- **Spec File**: ../../specs/plugins/global.yaml
- **Response Code**: 400

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 400

---

### ✅ PUT /orchestrator/plugin/global/migrate

- **Status**: PASS
- **Duration**: 112.848583ms
- **Spec File**: ../../specs/plugins/global.yaml
- **Response Code**: 200

---

### ❌ GET /orchestrator/plugin/global/list/global-variable

- **Status**: FAIL
- **Duration**: 112.569542ms
- **Spec File**: ../../specs/plugins/global.yaml
- **Response Code**: 400

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 400

---

### ❌ POST /orchestrator/user/role/group/v2

- **Status**: FAIL
- **Duration**: 115.95575ms
- **Spec File**: ../../specs/security/group-policy.yaml
- **Response Code**: 500

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 500

---

### ❌ PUT /orchestrator/user/role/group/v2

- **Status**: FAIL
- **Duration**: 114.958667ms
- **Spec File**: ../../specs/security/group-policy.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ✅ GET /orchestrator/user/role/group/v2

- **Status**: PASS
- **Duration**: 117.684583ms
- **Spec File**: ../../specs/security/group-policy.yaml
- **Response Code**: 200

---

### ❌ DELETE /orchestrator/user/role/group/{id}

- **Status**: FAIL
- **Duration**: 113.721875ms
- **Spec File**: ../../specs/security/group-policy.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ GET /orchestrator/user/role/group/{id}

- **Status**: FAIL
- **Duration**: 113.605542ms
- **Spec File**: ../../specs/security/group-policy.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ✅ GET /orchestrator/user/role/group

- **Status**: PASS
- **Duration**: 114.895708ms
- **Spec File**: ../../specs/security/group-policy.yaml
- **Response Code**: 200

---

### ❌ POST /orchestrator/user/role/group

- **Status**: FAIL
- **Duration**: 115.429875ms
- **Spec File**: ../../specs/security/group-policy.yaml
- **Response Code**: 500

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 500

---

### ❌ PUT /orchestrator/user/role/group

- **Status**: FAIL
- **Duration**: 116.359875ms
- **Spec File**: ../../specs/security/group-policy.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ DELETE /orchestrator/user/role/group/bulk

- **Status**: FAIL
- **Duration**: 114.222417ms
- **Spec File**: ../../specs/security/group-policy.yaml
- **Response Code**: 500

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 500

---

### ✅ GET /orchestrator/user/role/group/detailed/get

- **Status**: PASS
- **Duration**: 115.29875ms
- **Spec File**: ../../specs/security/group-policy.yaml
- **Response Code**: 200

---

### ❌ GET /orchestrator/user/role/group/search

- **Status**: FAIL
- **Duration**: 114.438458ms
- **Spec File**: ../../specs/security/group-policy.yaml
- **Response Code**: 400

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 400

---

### ❌ GET /orchestrator/app/helm/meta/info/{appId}

- **Status**: FAIL
- **Duration**: 116.466625ms
- **Spec File**: ../../specs/application/labels.yaml
- **Response Code**: 500

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 500

---

### ✅ GET /orchestrator/app/labels/list

- **Status**: PASS
- **Duration**: 116.036875ms
- **Spec File**: ../../specs/application/labels.yaml
- **Response Code**: 200

---

### ✅ GET /orchestrator/app/meta/info/{appId}

- **Status**: PASS
- **Duration**: 120.874458ms
- **Spec File**: ../../specs/application/labels.yaml
- **Response Code**: 200

---

### ❌ POST /v1beta1/deploy

- **Status**: FAIL
- **Duration**: 116.848958ms
- **Spec File**: ../../specs/jobs/bulk-actions.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ POST /v1beta1/unhibernate

- **Status**: FAIL
- **Duration**: 111.131166ms
- **Spec File**: ../../specs/jobs/bulk-actions.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ POST /orchestrator/v1beta1/hibernate

- **Status**: FAIL
- **Duration**: 110.535958ms
- **Spec File**: ../../specs/jobs/bulk-actions.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ✅ GET /orchestrator/api-token/webhook

- **Status**: PASS
- **Duration**: 116.986708ms
- **Spec File**: ../../specs/openapiClient/api/apiToken_api-openapi.yaml
- **Response Code**: 200

---

### ❌ DELETE /orchestrator/api-token/{id}

- **Status**: FAIL
- **Duration**: 115.989208ms
- **Spec File**: ../../specs/openapiClient/api/apiToken_api-openapi.yaml
- **Response Code**: 500

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 500

---

### ❌ PUT /orchestrator/api-token/{id}

- **Status**: FAIL
- **Duration**: 114.932ms
- **Spec File**: ../../specs/openapiClient/api/apiToken_api-openapi.yaml
- **Response Code**: 400

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 400

---

### ✅ GET /orchestrator/api-token

- **Status**: PASS
- **Duration**: 114.532166ms
- **Spec File**: ../../specs/openapiClient/api/apiToken_api-openapi.yaml
- **Response Code**: 200

---

### ❌ POST /orchestrator/api-token

- **Status**: FAIL
- **Duration**: 116.31775ms
- **Spec File**: ../../specs/openapiClient/api/apiToken_api-openapi.yaml
- **Response Code**: 400

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 400

---

### ❌ POST /orchestrator/user/resource/options/{kind}/{version}

- **Status**: FAIL
- **Duration**: 111.506541ms
- **Spec File**: ../../specs/userResource/userResource.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ GET /orchestrator/app/history/deployed-component/detail/{appId}/{pipelineId}/{id}

- **Status**: FAIL
- **Duration**: 114.690125ms
- **Spec File**: ../../specs/audit/definitions.yaml
- **Response Code**: 400

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 400

---

### ❌ GET /orchestrator/app/history/deployed-component/list/{appId}/{pipelineId}

- **Status**: FAIL
- **Duration**: 117.152042ms
- **Spec File**: ../../specs/audit/definitions.yaml
- **Response Code**: 400

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 400

---

### ✅ GET /orchestrator/app/history/deployed-configuration/{appId}/{pipelineId}/{wfrId}

- **Status**: PASS
- **Duration**: 116.824125ms
- **Spec File**: ../../specs/audit/definitions.yaml
- **Response Code**: 200

---

### ❌ POST /orchestrator/deployment/pipeline/configure

- **Status**: FAIL
- **Duration**: 116.200042ms
- **Spec File**: ../../specs/deployment/pipeline.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ GET /orchestrator/deployment/pipeline/history

- **Status**: FAIL
- **Duration**: 116.432917ms
- **Spec File**: ../../specs/deployment/pipeline.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ POST /orchestrator/deployment/pipeline/rollback

- **Status**: FAIL
- **Duration**: 111.665958ms
- **Spec File**: ../../specs/deployment/pipeline.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ POST /orchestrator/deployment/pipeline/trigger

- **Status**: FAIL
- **Duration**: 110.185834ms
- **Spec File**: ../../specs/deployment/pipeline.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ POST /orchestrtor/batch/v1beta1/cd-pipeline

- **Status**: FAIL
- **Duration**: 111.461708ms
- **Spec File**: ../../specs/environment/bulk-delete.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ GET /orchestrator/resource/urls

- **Status**: FAIL
- **Duration**: 108.489875ms
- **Spec File**: ../../specs/kubernetes/resources.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ GET /orchestrator/api-resources/{clusterId}

- **Status**: FAIL
- **Duration**: 111.260875ms
- **Spec File**: ../../specs/kubernetes/resources.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ POST /orchestrator/resource

- **Status**: FAIL
- **Duration**: 108.866833ms
- **Spec File**: ../../specs/kubernetes/resources.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ POST /orchestrator/resource/create

- **Status**: FAIL
- **Duration**: 110.0425ms
- **Spec File**: ../../specs/kubernetes/resources.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ DELETE /orchestrator/resource/delete

- **Status**: FAIL
- **Duration**: 110.016083ms
- **Spec File**: ../../specs/kubernetes/resources.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ GET /orchestrator/resource/inception/info

- **Status**: FAIL
- **Duration**: 112.375083ms
- **Spec File**: ../../specs/kubernetes/resources.yaml
- **Response Code**: 400

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 400

---

### ❌ PUT /orchestrator/resource/update

- **Status**: FAIL
- **Duration**: 108.525625ms
- **Spec File**: ../../specs/kubernetes/resources.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ POST /orchestrator/configmap/bulk/patch

- **Status**: FAIL
- **Duration**: 109.87ms
- **Spec File**: ../../specs/plugins/config-maps.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ POST /orchestrator/configmap/environment

- **Status**: FAIL
- **Duration**: 109.290125ms
- **Spec File**: ../../specs/plugins/config-maps.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ GET /orchestrator/configmap/environment/{appId}/{envId}

- **Status**: FAIL
- **Duration**: 111.331666ms
- **Spec File**: ../../specs/plugins/config-maps.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ DELETE /orchestrator/configmap/environment/{appId}/{envId}/{id}

- **Status**: FAIL
- **Duration**: 111.07325ms
- **Spec File**: ../../specs/plugins/config-maps.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ POST /orchestrator/configmap/global

- **Status**: FAIL
- **Duration**: 111.91875ms
- **Spec File**: ../../specs/plugins/config-maps.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ GET /orchestrator/configmap/global/edit/{appId}/{id}

- **Status**: FAIL
- **Duration**: 110.310166ms
- **Spec File**: ../../specs/plugins/config-maps.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ DELETE /orchestrator/configmap/global/{appId}/{id}

- **Status**: FAIL
- **Duration**: 110.121375ms
- **Spec File**: ../../specs/plugins/config-maps.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ GET /orchestrator/configmap/environment/edit/{appId}/{envId}/{id}

- **Status**: FAIL
- **Duration**: 117.081083ms
- **Spec File**: ../../specs/plugins/config-maps.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ GET /orchestrator/configmap/global/{appId}

- **Status**: FAIL
- **Duration**: 112.636208ms
- **Spec File**: ../../specs/plugins/config-maps.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

